<template>
  <div class="w-full h-full min-h-0">
    <!-- 拖拽上传区域 -->
    <div
      class="overflow-hidden relative rounded-xl border-2 border-dashed transition-all duration-200 border-border bg-card"
      :class="{
        'border-primary bg-accent': isDragging,
        'border-solid hover:border-primary hover:bg-accent': files.length > 0,
        'hover:border-primary hover:bg-accent': files.length === 0
      }" @dragenter="handleDragEnter" @dragover="handleDragOver" @dragleave="handleDragLeave" @drop="handleDropFiles">
      <div class="flex flex-col gap-2 items-center p-4 text-center sm:p-6">
        <div class="flex justify-center items-center w-12 h-12 rounded-full sm:w-16 sm:h-16 bg-muted">
          <Upload class="w-8 h-8 sm:w-12 sm:h-12 text-muted-foreground" />
        </div>

        <div class="max-w-96">
          <h3 class="mb-2 text-base font-semibold sm:text-lg text-foreground">拖拽文件到此处上传</h3>
        </div>

        <div class="flex flex-wrap gap-2 justify-center items-center">
          <Button @click="selectFiles" :disabled="!canAddMore">
            <Plus class="mr-2 w-4 h-4" />
            选择文件
          </Button>

          <Button @click="selectDirectories" :disabled="!canAddMore" variant="outline">
            <FolderOpen class="mr-2 w-4 h-4" />
            选择文件夹
          </Button>

          <!-- 隐藏的文件输入 -->
          <input ref="fileInputRef" type="file" :accept="accept" :multiple="multiple" class="hidden"
            @change="handleFileInputChange" />

          <!-- 隐藏的目录输入 -->
          <input ref="dirInputRef" type="file" webkitdirectory class="hidden" @change="handleFileInputChange" />
        </div>

        <div v-if="!canAddMore" class="text-sm font-medium text-destructive">
          已达到最大文件数量限制 ({{ maxFiles }})
        </div>
      </div>

      <!-- 拖拽遮罩 -->
      <div v-if="isDragging"
        class="flex absolute inset-0 z-10 justify-center items-center backdrop-blur-sm bg-primary/10">
        <div class="flex flex-col gap-2 items-center font-semibold text-primary">
          <Download class="w-8 h-8" />
          <span class="text-lg">释放以上传文件</span>
        </div>
      </div>
    </div>

    <!-- 文件列表 -->
    <div v-if="files.length > 0" class="flex flex-col flex-1 mt-6 min-h-0">
      <div class="flex flex-col gap-2 pb-2 mb-4 border-b sm:items-center sm:justify-between sm:gap-4 border-border">
        <h4 class="text-base font-semibold text-foreground">
          已选择 {{ files.length }} 个文件
          <span v-if="folderGroups.length > 0" class="text-sm font-normal text-muted-foreground">
            (来自 {{ folderGroups.length }} 个文件夹)
          </span>
        </h4>
        <div class="flex flex-wrap gap-2 items-center sm:gap-4">
          <span class="text-sm text-muted-foreground">总大小: {{ formatFileSize(totalSize) }}</span>
          <Button @click="toggleViewMode" variant="ghost" size="sm">
            <component :is="viewMode === 'grid' ? List : LayoutGrid" class="mr-1 w-4 h-4" />
            {{ viewMode === 'grid' ? '列表视图' : '网格视图' }}
          </Button>
          <Button variant="ghost" size="sm" @click="handleClearFiles">
            <Trash2 class="mr-1 w-4 h-4" />
            清空
          </Button>
        </div>
      </div>

      <div class="overflow-y-auto flex-1 rounded-lg border border-border bg-muted/30">
        <!-- 统一的文件和文件夹显示列表 -->
        <div class="grid p-3" :class="{
          'grid-cols-[repeat(auto-fill,minmax(80px,1fr))] sm:grid-cols-[repeat(auto-fill,minmax(90px,1fr))] lg:grid-cols-[repeat(auto-fill,minmax(100px,1fr))] gap-2': viewMode === 'grid',
          'grid-cols-1 gap-2': viewMode === 'list'
        }">
          <template v-for="item in displayItems" :key="item.id">
            <!-- 文件夹项 -->
            <FolderItem v-if="item.type === 'folder'" :folder="item" :view-mode="viewMode" @remove="removeFolderGroup"
              @toggle-expand="toggleFolderExpand" @file-remove="handleFileRemove" />

            <!-- 文件项 -->
            <FileItem v-else :file="item" :view-mode="viewMode" @remove="handleFileRemove" />
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { toast } from 'vue-sonner'
import {
  Upload,
  Plus,
  Download,
  Trash2,
  FolderOpen,
  LayoutGrid,
  List
} from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import FileItem from './FileItem.vue'
import FolderItem, { type FolderGroup } from './FolderItem.vue'
import { useFileUpload, type UseFileUploadOptions } from './composables/useFileUpload'
import { useDragAndDrop, type DragDropResult } from '@/composables/useDragAndDrop'
import { detectUploadStrategy, type UploadStrategy } from '@/lib/upload-utils'

// Props
const props = withDefaults(defineProps<UseFileUploadOptions & {
  modelValue?: File[]
}>(), {
  accept: '',
  multiple: true,
  maxSize: 100 * 1024 * 1024, // 100MB
  maxFiles: Infinity, // 移除文件数量限制
  allowDirectories: true
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [files: File[]]
  'files-change': [files: File[]]
  'error': [errors: string[]]
  'upload-strategy': [uploadStrategy: UploadStrategy]
}>()

// 文件上传逻辑 - 使用 useFileUpload 进行文件管理
const {
  files,
  totalSize,
  canAddMore,
  handleFileInput,
  removeFile,
  clearFiles,
  formatFileSize,
  accept,
  multiple,
  maxFiles,
  allowDirectories,
  processFiles
} = useFileUpload(props)

// 使用统一拖拽处理逻辑
const {
  isDragging,
  handleDragEnter,
  handleDragOver,
  handleDragLeave,
  handleDrop: handleUnifiedDrop
} = useDragAndDrop(
  {
    allowDirectories: props.allowDirectories,
    maxFiles: props.maxFiles,
    maxSize: props.maxSize,
    accept: props.accept,
    useElectronAPI: true
  },
  {
    onFilesProcessed: async (result: DragDropResult) => {
      if (result.files.length > 0) {
        // 转换为 File[] 并添加到文件列表
        const newFiles = result.files.map(f => f.file)
        await processFiles(newFiles)
        updateParentComponent()

        // 重新计算上传策略
        const allFiles = files.value.map(f => f.file)
        const uploadStrategy = detectUploadStrategy(allFiles)
        emit('upload-strategy', uploadStrategy)
      }
    },
    onError: (errors: string[]) => {
      handleErrors(errors)
    },
    onSmartPackTriggered: async (droppedFiles: File[], fileCount: number) => {
      // 在 Upload 组件中，智能打包触发时仍然添加文件到列表
      // 因为用户可能想要查看和修改文件列表
      console.log(`📦 Upload 组件检测到大量文件: ${fileCount} 个，添加到上传列表`)
      toast.info(`检测到大量文件 (${fileCount} 个)，已添加到上传列表。建议使用智能打包功能`, {
        duration: 6000,
      })

      // 将文件添加到上传列表
      await processFiles(droppedFiles)
      updateParentComponent()

      // 重新计算上传策略
      const allFiles = files.value.map((f: any) => f.file)
      const uploadStrategy = detectUploadStrategy(allFiles)
      emit('upload-strategy', uploadStrategy)
    }
  }
)

// 文件输入引用
const fileInputRef = ref<HTMLInputElement>()
const dirInputRef = ref<HTMLInputElement>()

// 视图模式
const viewMode = ref<'grid' | 'list'>('grid')
const expandedFolders = ref<Set<string>>(new Set())

// 计算文件夹分组
const folderGroups = computed<FolderGroup[]>(() => {
  const groups = new Map<string, FolderGroup>()

  files.value.forEach(file => {
    // 通过文件的webkitRelativePath获取文件夹路径
    const relativePath = (file.file as any).webkitRelativePath || ''

    if (relativePath) {
      const pathParts = relativePath.split('/')
      // 只使用根文件夹作为分组，避免创建过多的子文件夹分组
      const rootFolderName = pathParts[0]

      if (!groups.has(rootFolderName)) {
        groups.set(rootFolderName, {
          path: rootFolderName,
          name: rootFolderName,
          files: [],
          totalSize: 0,
          expanded: expandedFolders.value.has(rootFolderName)
        })
      }

      const group = groups.get(rootFolderName)!
      group.files.push(file)
      group.totalSize += file.size
    }
  })

  return Array.from(groups.values()).sort((a, b) => a.name.localeCompare(b.name))
})

// 单独文件（不属于任何文件夹）
const singleFiles = computed(() => {
  return files.value.filter(file => {
    const relativePath = (file.file as any).webkitRelativePath || ''
    return !relativePath
  })
})

// 混合显示项（文件夹 + 单独文件）
const displayItems = computed(() => {
  const items: Array<any> = []

  // 添加文件夹项
  folderGroups.value.forEach(group => {
    items.push({
      id: `folder-${group.path}`,
      type: 'folder',
      path: group.path,
      name: group.name,
      files: group.files,
      totalSize: group.totalSize,
      expanded: group.expanded
    })
  })

  // 添加单独文件项
  singleFiles.value.forEach(file => {
    items.push({
      ...file,
      type: 'file'
    })
  })

  return items
})

// 切换视图模式
const toggleViewMode = () => {
  viewMode.value = viewMode.value === 'grid' ? 'list' : 'grid'
}

// 切换文件夹展开状态
const toggleFolderExpand = (folderPath: string) => {
  if (expandedFolders.value.has(folderPath)) {
    expandedFolders.value.delete(folderPath)
  } else {
    expandedFolders.value.add(folderPath)
  }
}

// 移除整个文件夹组
const removeFolderGroup = (folderPath: string) => {
  const group = folderGroups.value.find(g => g.path === folderPath)
  if (group) {
    // 移除文件夹中的所有文件
    group.files.forEach(file => removeFile(file.id))
    expandedFolders.value.delete(folderPath)
    updateParentComponent()

    // 重新计算上传策略
    const allFiles = files.value.map(f => f.file)
    if (allFiles.length > 0) {
      const uploadStrategy = detectUploadStrategy(allFiles)
      emit('upload-strategy', uploadStrategy)
    } else {
      // 如果没有文件了，重置策略
      emit('upload-strategy', { type: 'single', folders: [], singleFiles: [], description: '无文件' })
    }
  }
}

// 选择文件 - 使用浏览器标准 API
const selectFiles = () => {
  fileInputRef.value?.click()
}

// 选择目录 - 使用Electron API支持空文件夹
const selectDirectories = async () => {
  if (!allowDirectories) return

  try {
    const api = (window as any).electronAPI
    if (api && api.tus && api.tus.createUploadFromFolderDialog) {
      // 使用Electron API选择文件夹，支持空文件夹
      const result = await api.tus.createUploadFromFolderDialog()

      if (result.success && result.data && result.data.taskIds) {
        console.log(`📁 通过Electron API选择文件夹，创建了 ${result.data.taskIds.length} 个上传任务`)

        // 直接启动已创建的后端任务，不要重新创建
        for (const taskId of result.data.taskIds) {
          try {
            console.log(`🚀 启动后端任务: ${taskId}`)
            await api.tus.startUpload(taskId)
          } catch (error) {
            console.error(`启动任务 ${taskId} 失败:`, error)
          }
        }

        console.log(`✅ 已启动 ${result.data.taskIds.length} 个后端上传任务`)
      } else {
        console.log('用户取消了文件夹选择或选择失败')
      }
    } else {
      // 降级到浏览器API
      dirInputRef.value?.click()
    }
  } catch (error) {
    console.error('选择文件夹失败:', error)
    // 降级到浏览器API
    dirInputRef.value?.click()
  }
}



// 处理文件输入变化
const handleFileInputChange = async (event: Event) => {
  const input = event.target as HTMLInputElement
  if (!input.files || input.files.length === 0) {
    return
  }

  const inputErrors = await handleFileInput(event)
  handleErrors(inputErrors)

  // 更新父组件后重新计算整体上传策略
  updateParentComponent()

  // 基于所有文件重新计算上传策略
  const allFiles = files.value.map(f => f.file)
  const uploadStrategy = detectUploadStrategy(allFiles)

  // 将上传策略传递给父组件
  emit('upload-strategy', uploadStrategy)
}

// 处理拖拽文件 - 使用统一的拖拽处理逻辑
const handleDropFiles = async (event: DragEvent) => {
  // 使用统一的拖拽处理逻辑
  await handleUnifiedDrop(event)
  // 其他逻辑已在 useDragAndDrop 的回调中处理
}

// 处理文件移除
const handleFileRemove = (fileId: string) => {
  removeFile(fileId)
  updateParentComponent()

  // 重新计算上传策略
  const allFiles = files.value.map(f => f.file)
  if (allFiles.length > 0) {
    const uploadStrategy = detectUploadStrategy(allFiles)
    emit('upload-strategy', uploadStrategy)
  }
}

// 处理清空文件
const handleClearFiles = () => {
  clearFiles()
  expandedFolders.value.clear()
  updateParentComponent()

  // 清空时重置上传策略
  emit('upload-strategy', { type: 'single', folders: [], singleFiles: [], description: '清空文件' })
}

// 处理错误信息
const handleErrors = (errors: string[]) => {
  if (errors.length > 0) {
    errors.forEach(error => {
      // 如果是文件数量限制的友好提示，显示为信息而不是错误
      if (error.includes('已自动选择前') || error.includes('只能再添加')) {
        toast.info(error)
      } else {
        toast.error(error)
      }
    })
    emit('error', errors)
  }
}

// 监听 modelValue 变化，支持外部预设文件
watch(() => props.modelValue, async (newFiles) => {
  if (newFiles && newFiles.length > 0) {
    // 避免循环更新：只有当外部文件与当前文件不同时才处理
    const currentFiles = files.value.map(f => f.file)
    const isSameFiles = newFiles.length === currentFiles.length &&
      newFiles.every((file, index) => {
        const currentFile = currentFiles[index]
        return currentFile &&
          file.name === currentFile.name &&
          file.size === currentFile.size &&
          file.lastModified === currentFile.lastModified
      })

    if (!isSameFiles) {
      console.log(`📁 FileUploadArea: 接收到外部预设文件 ${newFiles.length} 个`)
      await processFiles(newFiles)

      // 重新计算上传策略
      const allFiles = files.value.map(f => f.file)
      const uploadStrategy = detectUploadStrategy(allFiles)
      emit('upload-strategy', uploadStrategy)
    }
  }
}, { immediate: true })

// 更新父组件
const updateParentComponent = () => {
  const fileList = files.value.map(f => f.file)
  emit('update:modelValue', fileList)
  emit('files-change', fileList)
}
</script>
