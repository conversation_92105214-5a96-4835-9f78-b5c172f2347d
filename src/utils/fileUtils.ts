/**
 * 文件处理工具函数
 * 提供文件对象创建、路径处理等通用功能
 */

/**
 * 文件信息接口
 */
export interface FileInfo {
  /** 文件绝对路径 */
  path: string;
  /** 文件名 */
  name?: string;
  /** 相对路径（用于保持目录结构） */
  relativePath?: string;
  /** 是否为目录 */
  isDirectory?: boolean;
  /** 是否为空目录 */
  isEmpty?: boolean;
  /** 文件大小 */
  size?: number;
  /** 最后修改时间 */
  mtime?: number;
}

/**
 * 为空文件夹创建特殊的 File 对象
 *
 * @param fileInfo 文件信息对象
 * @returns 配置好的空文件夹 File 对象
 */
export function createEmptyFolderFile(fileInfo: FileInfo): File {
  // 提取文件夹名称（支持 Windows 和 Unix 路径分隔符）
  const folderName = fileInfo.name || fileInfo.path.split(/[/\\]/).pop() || "unknown";

  // 创建空文件夹的 File 对象
  const file = new File([], folderName, {
    type: "application/x-directory",
    lastModified: fileInfo.mtime || Date.now(),
  });

  // 添加路径信息到 File 对象
  Object.defineProperty(file, "path", {
    value: fileInfo.path,
    writable: false,
  });

  Object.defineProperty(file, "size", {
    value: 0,
    writable: false,
  });

  // 设置相对路径（如果提供）
  if (fileInfo.relativePath) {
    Object.defineProperty(file, "webkitRelativePath", {
      value: fileInfo.relativePath,
      writable: false,
    });
  }

  // 标记为空文件夹
  Object.defineProperty(file, "isFolder", {
    value: true,
    writable: false,
  });

  Object.defineProperty(file, "isEmpty", {
    value: true,
    writable: false,
  });

  return file;
}

/**
 * 检查文件信息是否表示空文件夹
 *
 * @param fileInfo 文件信息对象
 * @returns 是否为空文件夹
 */
export function isEmptyFolder(fileInfo: FileInfo): boolean {
  return Boolean(fileInfo.isDirectory && fileInfo.isEmpty);
}

/**
 * 从文件路径提取文件名（跨平台兼容）
 *
 * @param filePath 文件路径
 * @returns 文件名
 */
export function extractFileName(filePath: string): string {
  return filePath.split(/[/\\]/).pop() || "unknown";
}

/**
 * 检查相对路径是否包含目录结构
 *
 * @param relativePath 相对路径
 * @param fileName 文件名
 * @returns 是否包含目录结构
 */
export function hasPathStructure(relativePath?: string, fileName?: string): boolean {
  if (!relativePath || !fileName) return false;

  return relativePath !== fileName && (relativePath.includes("/") || relativePath.includes("\\"));
}
